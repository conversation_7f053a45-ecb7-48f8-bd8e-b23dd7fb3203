plugins {
    id "org.springframework.boot"
    id "io.spring.dependency-management"
}

dependencies {
    // protobuf-java
    implementation("com.google.protobuf:protobuf-java:${protobufVersion}")
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:${springDocsVersion}")
    implementation(project(":springdocs-bridge-protobuf"))
}

apply from: "${rootDir}/gradle/protobuf.gradle"